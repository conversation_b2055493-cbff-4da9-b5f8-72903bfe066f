<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('otp_requests', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number', 20)->index();
            $table->string('request_id')->unique();
            $table->timestamp('expires_at')->index();
            $table->string('status', 20)->default('pending'); // pending, verified, expired, failed
            $table->json('metadata')->nullable(); // Store additional data like language, length, etc.
            $table->timestamps();

            // Index for efficient lookups
            $table->index(['phone_number', 'status']);
            $table->index(['phone_number', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('otp_requests');
    }
};
