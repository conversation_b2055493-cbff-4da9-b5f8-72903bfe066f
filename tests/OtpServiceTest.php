<?php

namespace Tests;

use Orchestra\Testbench\TestCase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Monsefeledrisse\Eshaarsms\OtpService;
use Monsefeledrisse\Eshaarsms\EshaarClient;
use Monsefeledrisse\Eshaarsms\Models\OtpRequest;
use Monsefeledrisse\Eshaarsms\Exceptions\OtpException;
use Monsefeledrisse\Eshaarsms\EshaarServiceProvider;

class OtpServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $otpService;
    protected $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');

        // Mock the EshaarClient
        $this->client = $this->createMock(EshaarClient::class);
        $this->otpService = new OtpService($this->client);
    }

    protected function getPackageProviders($app)
    {
        return [EshaarServiceProvider::class];
    }

    protected function getEnvironmentSetUp($app)
    {
        // Setup test database
        $app['config']->set('database.default', 'testing');
        $app['config']->set('database.connections.testing', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // Setup Eshaar config
        $app['config']->set('eshaar.api_key', 'test-key');
        $app['config']->set('eshaar.sender', 'TEST');
    }

    public function test_send_otp_successfully()
    {
        // Mock successful API response
        $this->client->expects($this->once())
            ->method('initiateOtp')
            ->with('218912345678', 'en', 6, 10, null)
            ->willReturn([
                'request_id' => 'test-request-id-123',
                'cost' => 0.05
            ]);

        // Mock cache for rate limiting
        Cache::shouldReceive('get')
            ->with('eshaar_rate_limit_218912345678', 0)
            ->andReturn(0);

        Cache::shouldReceive('put')
            ->with('eshaar_rate_limit_218912345678', 1, \Mockery::any());

        $result = $this->otpService->send('218912345678');

        $this->assertTrue($result['success']);
        $this->assertEquals('test-request-id-123', $result['request_id']);
        $this->assertEquals(0.05, $result['cost']);

        // Verify database record was created
        $this->assertDatabaseHas('otp_requests', [
            'phone_number' => '218912345678',
            'request_id' => 'test-request-id-123',
            'status' => OtpRequest::STATUS_PENDING,
        ]);
    }

    public function test_verify_otp_successfully()
    {
        // Create an OTP request in the database
        $otpRequest = OtpRequest::createRequest(
            '218912345678',
            'test-request-id-123',
            10
        );

        // Mock successful verification
        $this->client->expects($this->once())
            ->method('verifyOtp')
            ->with('test-request-id-123', '123456')
            ->willReturn([
                'message' => 'OTP verified successfully'
            ]);

        $result = $this->otpService->verify('218912345678', '123456');

        $this->assertTrue($result['success']);
        $this->assertEquals('OTP verified successfully', $result['message']);

        // Verify database record was updated
        $this->assertDatabaseHas('otp_requests', [
            'id' => $otpRequest->id,
            'status' => OtpRequest::STATUS_VERIFIED,
        ]);
    }

    public function test_verify_otp_fails_when_no_request_found()
    {
        // No OTP request in database
        $this->expectException(OtpException::class);
        $this->expectExceptionMessage('No OTP request found for this phone number or OTP has expired.');

        $this->otpService->verify('218912345678', '123456');
    }

    public function test_verify_otp_fails_when_request_expired()
    {
        // Create an expired OTP request
        $otpRequest = OtpRequest::create([
            'phone_number' => '218912345678',
            'request_id' => 'test-request-id-123',
            'expires_at' => now()->subMinutes(5), // Expired 5 minutes ago
            'status' => OtpRequest::STATUS_PENDING,
        ]);

        $this->expectException(OtpException::class);
        $this->expectExceptionMessage('No OTP request found for this phone number or OTP has expired.');

        $this->otpService->verify('218912345678', '123456');
    }

    public function test_rate_limiting_prevents_multiple_requests()
    {
        // Mock rate limit exceeded
        Cache::shouldReceive('get')
            ->with('eshaar_rate_limit_218912345678', 0)
            ->andReturn(3); // Max attempts reached

        $this->expectException(OtpException::class);
        $this->expectExceptionMessage('Rate limit exceeded. Please try again later.');

        $this->otpService->send('218912345678');
    }

    public function test_phone_number_normalization()
    {
        // Test phone number normalization
        $reflection = new \ReflectionClass($this->otpService);
        $method = $reflection->getMethod('normalizePhoneNumber');
        $method->setAccessible(true);

        // Test adding country code
        $result = $method->invokeArgs($this->otpService, ['912345678']);
        $this->assertEquals('218912345678', $result);

        // Test removing non-numeric characters
        $result = $method->invokeArgs($this->otpService, ['+218-91-234-5678']);
        $this->assertEquals('218912345678', $result);

        // Test already normalized number
        $result = $method->invokeArgs($this->otpService, ['218912345678']);
        $this->assertEquals('218912345678', $result);
    }

    public function test_can_resend_otp()
    {
        Cache::shouldReceive('get')
            ->with('eshaar_rate_limit_218912345678', 0)
            ->andReturn(2); // Below max attempts

        $result = $this->otpService->canResend('218912345678');
        $this->assertTrue($result);
    }

    public function test_cannot_resend_otp_when_rate_limited()
    {
        Cache::shouldReceive('get')
            ->with('eshaar_rate_limit_218912345678', 0)
            ->andReturn(3); // Max attempts reached

        $result = $this->otpService->canResend('218912345678');
        $this->assertFalse($result);
    }

    public function test_reset_rate_limit()
    {
        Cache::shouldReceive('forget')
            ->once()
            ->with('eshaar_rate_limit_218912345678');

        $this->otpService->resetRateLimit('218912345678');

        // If we reach here without exception, the test passes
        $this->assertTrue(true);
    }

    public function test_has_active_request()
    {
        // No active request initially
        $this->assertFalse($this->otpService->hasActiveRequest('218912345678'));

        // Create an active request
        OtpRequest::createRequest('218912345678', 'test-request-id-123', 10);

        $this->assertTrue($this->otpService->hasActiveRequest('218912345678'));
    }

    public function test_get_statistics()
    {
        // Create some test data
        OtpRequest::createRequest('218912345678', 'req-1', 10);

        $verifiedRequest = OtpRequest::createRequest('218912345678', 'req-2', 10);
        $verifiedRequest->markAsVerified();

        $expiredRequest = OtpRequest::create([
            'phone_number' => '218912345678',
            'request_id' => 'req-3',
            'expires_at' => now()->subMinutes(5),
            'status' => OtpRequest::STATUS_EXPIRED,
        ]);

        $stats = $this->otpService->getStatistics('218912345678');

        $this->assertEquals(3, $stats['total']);
        $this->assertEquals(1, $stats['verified']);
        $this->assertEquals(1, $stats['expired']);
        $this->assertEquals(1, $stats['pending']);
        $this->assertEquals(33.33, $stats['success_rate']);
    }

    public function test_cleanup_expired_requests()
    {
        // Create some test requests
        $activeRequest = OtpRequest::createRequest('218912345678', 'active-req', 10);

        $expiredRequest = OtpRequest::create([
            'phone_number' => '218912345678',
            'request_id' => 'expired-req',
            'expires_at' => now()->subMinutes(5),
            'status' => OtpRequest::STATUS_PENDING,
        ]);

        // Run cleanup
        $cleanedCount = $this->otpService->cleanup();

        $this->assertEquals(1, $cleanedCount);

        // Verify the expired request was marked as expired
        $expiredRequest->refresh();
        $this->assertEquals(OtpRequest::STATUS_EXPIRED, $expiredRequest->status);

        // Verify the active request is still pending
        $activeRequest->refresh();
        $this->assertEquals(OtpRequest::STATUS_PENDING, $activeRequest->status);
    }

    public function test_get_recent_requests()
    {
        // Create some test requests
        $request1 = OtpRequest::createRequest('218912345678', 'req-1', 10);
        $request2 = OtpRequest::createRequest('218912345678', 'req-2', 10);

        $recentRequests = $this->otpService->getRecentRequests('218912345678', 5);

        $this->assertCount(2, $recentRequests);
        $this->assertEquals('req-2', $recentRequests->first()->request_id); // Most recent first
        $this->assertEquals('req-1', $recentRequests->last()->request_id);
    }
}
