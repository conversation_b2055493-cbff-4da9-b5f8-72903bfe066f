
# Eshaar SMS Laravel Package

A comprehensive Laravel package for integrating with the Eshaar SMS API, providing OTP (One-Time Password) functionality and SMS messaging capabilities.

## Features

- 🔐 **OTP Generation & Verification** - Secure OTP handling using Eshaar's API
- 📱 **SMS Messaging** - Send SMS messages through Eshaar API
- 🚦 **Rate Limiting** - Built-in rate limiting to prevent abuse
- 🌍 **Multi-language Support** - Support for English and Arabic OTP messages
- 📧 **Laravel Notifications** - Integration with Lara<PERSON>'s notification system
- 🎭 **Facades** - Easy-to-use facades for quick access
- 🔧 **Configurable** - Extensive configuration options
- 📊 **Logging** - Comprehensive logging for debugging and monitoring
- 🛡️ **Error Handling** - Robust error handling with custom exceptions

## Installation

Install the package via Composer:

```bash
composer require monsefeledrisse/eshaarsms
```

### Publish Configuration and Migrations

Publish the configuration file:

```bash
php artisan vendor:publish --provider="Monsefeledrisse\Eshaarsms\EshaarServiceProvider" --tag="config"
```

Publish and run the migrations:

```bash
php artisan vendor:publish --provider="Monsefeledrisse\Eshaarsms\EshaarServiceProvider" --tag="migrations"
php artisan migrate
```

### Environment Configuration

Add the following environment variables to your `.env` file:

```env
ESHAAR_API_KEY=your_api_key_here
ESHAAR_SENDER=YourSender
ESHAAR_PAYMENT_TYPE=postpaid
ESHAAR_OTP_LENGTH=6
ESHAAR_OTP_EXPIRY=10
ESHAAR_OTP_LANGUAGE=en
ESHAAR_RATE_LIMIT_MAX_ATTEMPTS=3
ESHAAR_RATE_LIMIT_WINDOW_MINUTES=60
```

## Quick Start

### Sending OTP

```php
use Monsefeledrisse\Eshaarsms\Facades\EshaarOtp;

// Send OTP
$result = EshaarOtp::send('218912345678');

if ($result['success']) {
    echo "OTP sent successfully!";
    echo "Request ID: " . $result['request_id'];
}
```

### Verifying OTP

```php
use Monsefeledrisse\Eshaarsms\Facades\EshaarOtp;

// Verify OTP
$result = EshaarOtp::verify('218912345678', '123456');

if ($result['success']) {
    echo "OTP verified successfully!";
} else {
    echo "Invalid OTP!";
}
```

### Sending SMS

```php
use Monsefeledrisse\Eshaarsms\Facades\Eshaar;

$result = Eshaar::sendSms('218912345678', 'Hello from Eshaar!');
```

## Advanced Usage

### Using with Models

Add the `HasPhoneNumber` trait to your User model:

```php
use Monsefeledrisse\Eshaarsms\Traits\HasPhoneNumber;

class User extends Authenticatable
{
    use HasPhoneNumber;

    // Your model code...
}
```

Now you can use OTP methods directly on your model:

```php
$user = User::find(1);

// Send OTP
$result = $user->sendOtp();

// Verify OTP
$result = $user->verifyOtp('123456');

// Check if can resend
if ($user->canResendOtp()) {
    $user->sendOtp();
}

// Get OTP statistics
$stats = $user->getOtpStatistics();
echo "Success rate: " . $stats['success_rate'] . "%";

// Get recent OTP requests
$recentRequests = $user->getRecentOtpRequests(5);

// Check if user has active OTP request
if ($user->hasActiveOtpRequest()) {
    echo "User has an active OTP request";
}
```

### Using Laravel Notifications

Create a notification:

```php
use Monsefeledrisse\Eshaarsms\Notifications\OtpNotification;

$user->notify(new OtpNotification('123456'));
```

### Custom Options

Send OTP with custom options:

```php
$result = EshaarOtp::send('218912345678', [
    'language' => 'ar',
    'length' => 4,
    'expiry' => 5,
    'sender' => 'CustomSender'
]);
```

## Configuration

The configuration file `config/eshaar.php` provides extensive customization options:

```php
return [
    // API credentials
    'api_key' => env('ESHAAR_API_KEY', ''),
    'base_url' => env('ESHAAR_BASE_URL', 'https://sms.lamah.com'),
    'sender' => env('ESHAAR_SENDER', 'ESHAAR'),
    'payment_type' => env('ESHAAR_PAYMENT_TYPE', 'postpaid'),

    // OTP settings
    'otp_length' => env('ESHAAR_OTP_LENGTH', 6),
    'otp_expiry' => env('ESHAAR_OTP_EXPIRY', 10),
    'otp_language' => env('ESHAAR_OTP_LANGUAGE', 'en'),

    // Rate limiting
    'rate_limit' => [
        'max_attempts' => env('ESHAAR_RATE_LIMIT_MAX_ATTEMPTS', 3),
        'window_minutes' => env('ESHAAR_RATE_LIMIT_WINDOW_MINUTES', 60),
    ],

    // Phone number settings
    'default_country_code' => env('ESHAAR_DEFAULT_COUNTRY_CODE', '218'),
    'phone_validation' => [
        'enabled' => env('ESHAAR_PHONE_VALIDATION_ENABLED', true),
        'min_length' => env('ESHAAR_PHONE_MIN_LENGTH', 9),
        'max_length' => env('ESHAAR_PHONE_MAX_LENGTH', 15),
    ],
];
```

## Error Handling

The package provides comprehensive error handling with custom exceptions:

```php
use Monsefeledrisse\Eshaarsms\Exceptions\OtpException;
use Monsefeledrisse\Eshaarsms\Exceptions\EshaarApiException;

try {
    $result = EshaarOtp::send('218912345678');
} catch (OtpException $e) {
    // Handle OTP-specific errors
    echo "OTP Error: " . $e->getMessage();
} catch (EshaarApiException $e) {
    // Handle API errors
    echo "API Error: " . $e->getMessage();
    echo "Status Code: " . $e->getStatusCode();
}
```

## Rate Limiting

The package includes built-in rate limiting to prevent abuse:

```php
// Check if OTP can be sent
if (EshaarOtp::canResend('218912345678')) {
    $result = EshaarOtp::send('218912345678');
} else {
    echo "Rate limit exceeded. Please try again later.";
}

// Reset rate limit (admin function)
EshaarOtp::resetRateLimit('218912345678');

// Get OTP statistics
$stats = EshaarOtp::getStatistics('218912345678');
echo "Total requests: " . $stats['total'];
echo "Success rate: " . $stats['success_rate'] . "%";

// Get recent OTP requests
$recentRequests = EshaarOtp::getRecentRequests('218912345678', 10);

// Check if phone number has active OTP request
if (EshaarOtp::hasActiveRequest('218912345678')) {
    echo "Phone number has an active OTP request";
}
```

## API Methods

### EshaarOtp Facade

```php
// Send OTP
EshaarOtp::send($phoneNumber, $options = [])

// Verify OTP
EshaarOtp::verify($phoneNumber, $code)

// Check if can resend
EshaarOtp::canResend($phoneNumber)

// Reset rate limit
EshaarOtp::resetRateLimit($phoneNumber)

// Get statistics
EshaarOtp::getStatistics($phoneNumber, $days = 7)

// Get recent requests
EshaarOtp::getRecentRequests($phoneNumber, $limit = 10)

// Check for active request
EshaarOtp::hasActiveRequest($phoneNumber)

// Cleanup old requests
EshaarOtp::cleanup()
```

### Eshaar Facade

```php
// Send SMS
Eshaar::sendSms($phoneNumber, $message, $sender = null)

// Initiate OTP
Eshaar::initiateOtp($phoneNumber, $language = 'en', $length = 6, $expiration = 10, $sender = null)

// Verify OTP
Eshaar::verifyOtp($requestId, $code)

// Get account balance
Eshaar::getBalance()
```

## Database Management

### OTP Request Storage

The package stores OTP requests in a database table for better persistence and audit trails. The `otp_requests` table includes:

- `phone_number` - The phone number the OTP was sent to
- `request_id` - The unique request ID from Eshaar API
- `expires_at` - When the OTP expires
- `status` - Current status (pending, verified, expired, failed)
- `metadata` - Additional data (language, length, cost, etc.)

### Console Commands

Clean up old OTP requests:

```bash
# Clean up requests older than 30 days
php artisan eshaar:cleanup-otp-requests

# Dry run to see what would be deleted
php artisan eshaar:cleanup-otp-requests --dry-run

# Clean up requests older than 7 days
php artisan eshaar:cleanup-otp-requests --days=7
```

### Scheduled Cleanup

Add this to your `app/Console/Kernel.php` to automatically clean up old requests:

```php
protected function schedule(Schedule $schedule)
{
    // Clean up OTP requests older than 30 days, daily at 2 AM
    $schedule->command('eshaar:cleanup-otp-requests --days=30')
             ->dailyAt('02:00');
}
```

## Testing

To run the tests:

```bash
composer test
```

## Contributing

Please see [CONTRIBUTING.md](CONTRIBUTING.md) for details.

## Security

If you discover any security-related issues, <NAME_EMAIL> instead of using the issue tracker.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
