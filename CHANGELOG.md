# Changelog

All notable changes to the Eshaar SMS Laravel package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2025-06-16

### Added
- **Database Storage for OTP Requests**
  - New `otp_requests` table for persistent OTP request tracking
  - Database migration for OTP request storage
  - Eloquent model `OtpRequest` with comprehensive functionality
  - Better audit trails and persistence across cache clearing operations

- **Enhanced OTP Management**
  - OTP request statistics and analytics
  - Recent OTP requests retrieval
  - Active OTP request checking
  - Automatic cleanup of expired requests
  - Status tracking (pending, verified, expired, failed)

- **Console Commands**
  - `eshaar:cleanup-otp-requests` command for cleaning old requests
  - Dry-run support for safe cleanup operations
  - Configurable cleanup periods

- **Improved Model Integration**
  - Additional methods in `HasPhoneNumber` trait
  - OTP statistics for models
  - Recent OTP requests for models
  - Active OTP request checking for models

### Changed
- **OTP Storage Method**: Moved from cache-only to database storage for request IDs
- **Rate Limiting**: Maintained cache-based rate limiting for performance
- **Service Methods**: Enhanced with database-backed functionality

### Technical Improvements
- Better data persistence and reliability
- Enhanced audit capabilities
- Improved error tracking and debugging
- More comprehensive testing with database integration

## [1.0.0] - 2025-06-16

### Added
- **Core OTP Functionality**
  - Integration with Eshaar SMS API for OTP generation and verification
  - Automatic OTP sending using Eshaar's `/api/otp/initiate` endpoint
  - OTP verification using Eshaar's `/api/otp/verify` endpoint
  - Support for custom OTP length, expiration, and language

- **SMS Messaging**
  - Direct SMS sending through Eshaar's `/api/sms/messages` endpoint
  - Support for custom sender names and payment types

- **Rate Limiting**
  - Built-in rate limiting to prevent OTP abuse
  - Configurable max attempts and time windows
  - Rate limit reset functionality for admin use

- **Multi-language Support**
  - Support for English and Arabic OTP messages
  - Configurable default language settings

- **Laravel Integration**
  - Service Provider with automatic registration
  - Configuration file with extensive options
  - Laravel Notification Channel for OTP sending
  - Facades for easy access (`EshaarOtp` and `Eshaar`)

- **Model Integration**
  - `HasPhoneNumber` trait for easy model integration
  - Automatic phone number routing for notifications
  - Model-level OTP methods (`sendOtp`, `verifyOtp`, etc.)

- **Error Handling**
  - Custom exception classes for different error types
  - Comprehensive error logging and debugging
  - Graceful handling of API failures

- **Phone Number Handling**
  - Automatic phone number normalization
  - Support for Libya country code (+218) by default
  - Configurable phone number validation

- **Configuration Options**
  - Extensive configuration file with environment variable support
  - Configurable API endpoints, credentials, and settings
  - Rate limiting configuration
  - Phone number validation settings

- **Testing Support**
  - PHPUnit test suite with comprehensive coverage
  - Mock implementations for testing
  - Example usage files and documentation

- **Documentation**
  - Comprehensive README with usage examples
  - API reference documentation
  - Configuration guide
  - Error handling examples

### Technical Details
- **API Integration**: Uses Eshaar's official API endpoints
- **Caching**: Uses Laravel's cache system for OTP storage and rate limiting
- **Logging**: Comprehensive logging for debugging and monitoring
- **Dependencies**: Compatible with Laravel 10.x, 11.x, and 12.x
- **PHP Version**: Requires PHP 8.1 or higher

### Package Structure
```
src/
├── Channels/           # Laravel notification channels
├── Exceptions/         # Custom exception classes
├── Facades/           # Laravel facades
├── Notifications/     # Pre-built notification classes
├── Traits/           # Reusable traits for models
├── EshaarClient.php  # Main API client
├── OtpService.php    # OTP service implementation
└── EshaarServiceProvider.php # Laravel service provider

config/
└── eshaar.php        # Configuration file

tests/
└── OtpServiceTest.php # Test suite

examples/
└── basic-usage.php   # Usage examples
```

### Configuration
The package includes extensive configuration options:
- API credentials and endpoints
- OTP settings (length, expiry, language)
- Rate limiting configuration
- Phone number validation settings
- Logging and debugging options

### Breaking Changes
- This is the initial release, no breaking changes

### Security
- Secure OTP handling using Eshaar's API
- Rate limiting to prevent abuse
- Input validation and sanitization
- Secure API key handling

### Performance
- Efficient caching for rate limiting and OTP storage
- Minimal API calls through proper request/response handling
- Optimized phone number normalization

### Compatibility
- Laravel 10.x, 11.x, 12.x
- PHP 8.1+
- Eshaar SMS API v1
