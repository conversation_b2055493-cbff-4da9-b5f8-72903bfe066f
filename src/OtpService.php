<?php

namespace Monsefeledrisse\Eshaarsms;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class OtpService
{
    protected $client;
    
    public function __construct(EshaarClient $client)
    {
        $this->client = $client;
    }
    
    /**
     * Generate and send an OTP to the given phone number
     *
     * @param string $phoneNumber
     * @param int $length
     * @param int $expiry
     * @return string
     */
    public function send($phoneNumber, $length = 6, $expiry = 10)
    {
        // Generate OTP
        $otp = $this->generate($length);
        
        // Store OTP in cache
        $this->store($phoneNumber, $otp, $expiry);
        
        // Send OTP via SMS
        $message = $this->getMessage($otp);
        $this->client->sendSms($phoneNumber, $message);
        
        return $otp;
    }
    
    /**
     * Verify the OTP for a given phone number
     *
     * @param string $phoneNumber
     * @param string $otp
     * @return bool
     */
    public function verify($phoneNumber, $otp)
    {
        $key = $this->getCacheKey($phoneNumber);
        $cachedOtp = Cache::get($key);
        
        if ($cachedOtp && $cachedOtp === $otp) {
            Cache::forget($key);
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate a random OTP code
     *
     * @param int $length
     * @return string
     */
    protected function generate($length)
    {
        return (string) random_int(
            10 ** ($length - 1), 
            (10 ** $length) - 1
        );
    }
    
    /**
     * Store OTP in cache
     *
     * @param string $phoneNumber
     * @param string $otp
     * @param int $expiry
     * @return void
     */
    protected function store($phoneNumber, $otp, $expiry)
    {
        $key = $this->getCacheKey($phoneNumber);
        Cache::put($key, $otp, now()->addMinutes($expiry));
    }
    
    /**
     * Get the cache key for a phone number
     *
     * @param string $phoneNumber
     * @return string
     */
    protected function getCacheKey($phoneNumber)
    {
        return 'eshaar_otp_' . $phoneNumber;
    }
    
    /**
     * Get the SMS message with OTP
     *
     * @param string $otp
     * @return string
     */
    protected function getMessage($otp)
    {
        $template = config('eshaar.message_template', 'Your verification code is: {otp}');
        return str_replace('{otp}', $otp, $template);
    }
}