<?php

namespace Monsefeledrisse\Eshaarsms;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Monsefeledrisse\Eshaarsms\Exceptions\OtpException;
use Monsefeledrisse\Eshaarsms\Exceptions\EshaarApiException;

class OtpService
{
    public function __construct(protected \Monsefeledrisse\Eshaarsms\EshaarClient $client)
    {
    }

    /**
     * Send an OTP to the given phone number using Eshaar API
     *
     * @param string $phoneNumber
     * @return array
     * @throws OtpException
     */
    public function send($phoneNumber, array $options = [])
    {
        try {
            // Check rate limiting
            if ($this->isRateLimited($phoneNumber)) {
                throw new OtpException('Rate limit exceeded. Please try again later.');
            }

            $language = $options['language'] ?? config('eshaar.otp_language', 'en');
            $length = $options['length'] ?? config('eshaar.otp_length', 6);
            $expiry = $options['expiry'] ?? config('eshaar.otp_expiry', 10);
            $sender = $options['sender'] ?? null;

            // Validate phone number
            $phoneNumber = $this->normalizePhoneNumber($phoneNumber);

            // Send OTP via Eshaar API
            $response = $this->client->initiateOtp($phoneNumber, $language, $length, $expiry, $sender);

            // Store request_id for verification
            $this->storeRequestId($phoneNumber, $response['request_id'], $expiry);

            // Update rate limiting
            $this->updateRateLimit($phoneNumber);

            // Log the OTP request
            Log::info('OTP sent successfully', [
                'phone_number' => $phoneNumber,
                'request_id' => $response['request_id'],
                'cost' => $response['cost'] ?? null,
            ]);

            return [
                'success' => true,
                'request_id' => $response['request_id'],
                'cost' => $response['cost'] ?? null,
                'message' => 'OTP sent successfully',
            ];

        } catch (EshaarApiException $e) {
            Log::error('Failed to send OTP', [
                'phone_number' => $phoneNumber,
                'error' => $e->getMessage(),
                'status_code' => $e->getStatusCode(),
            ]);

            throw new OtpException('Failed to send OTP: ' . $e->getMessage(), $e->getStatusCode(), $e);
        }
    }
    
    /**
     * Verify the OTP for a given phone number using Eshaar API
     *
     * @param string $phoneNumber
     * @param string $code
     * @return array
     * @throws OtpException
     */
    public function verify($phoneNumber, $code)
    {
        try {
            $phoneNumber = $this->normalizePhoneNumber($phoneNumber);

            // Get stored request_id
            $requestId = $this->getRequestId($phoneNumber);

            if (!$requestId) {
                throw new OtpException('No OTP request found for this phone number or OTP has expired.');
            }

            // Verify OTP via Eshaar API
            $response = $this->client->verifyOtp($requestId, $code);

            // Clear stored request_id
            $this->clearRequestId($phoneNumber);

            // Log successful verification
            Log::info('OTP verified successfully', [
                'phone_number' => $phoneNumber,
                'request_id' => $requestId,
            ]);

            return [
                'success' => true,
                'message' => $response['message'] ?? 'OTP verified successfully',
            ];

        } catch (EshaarApiException $e) {
            Log::error('Failed to verify OTP', [
                'phone_number' => $phoneNumber,
                'error' => $e->getMessage(),
                'status_code' => $e->getStatusCode(),
            ]);

            throw new OtpException('Failed to verify OTP: ' . $e->getMessage(), $e->getStatusCode(), $e);
        }
    }
    
    /**
     * Store request_id for OTP verification
     *
     * @param string $requestId
     * @param int $expiry
     * @return void
     */
    protected function storeRequestId(string $phoneNumber, $requestId, $expiry)
    {
        $key = $this->getRequestIdCacheKey($phoneNumber);
        Cache::put($key, $requestId, now()->addMinutes($expiry));
    }

    /**
     * Get stored request_id for phone number
     *
     * @return string|null
     */
    protected function getRequestId(string $phoneNumber)
    {
        $key = $this->getRequestIdCacheKey($phoneNumber);
        return Cache::get($key);
    }

    /**
     * Clear stored request_id
     *
     * @return void
     */
    protected function clearRequestId(string $phoneNumber)
    {
        $key = $this->getRequestIdCacheKey($phoneNumber);
        Cache::forget($key);
    }

    /**
     * Get the cache key for request_id storage
     */
    protected function getRequestIdCacheKey(string $phoneNumber): string
    {
        return 'eshaar_otp_request_' . $phoneNumber;
    }

    /**
     * Normalize phone number format
     *
     * @param string $phoneNumber
     * @return string
     */
    protected function normalizePhoneNumber($phoneNumber)
    {
        // Remove all non-numeric characters
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Add country code if not present (assuming Libya +218)
        if (!str_starts_with((string) $phoneNumber, '218') && strlen((string) $phoneNumber) === 9) {
            return '218' . $phoneNumber;
        }

        return $phoneNumber;
    }

    /**
     * Check if phone number is rate limited
     */
    protected function isRateLimited(string $phoneNumber): bool
    {
        $key = $this->getRateLimitCacheKey($phoneNumber);
        $attempts = Cache::get($key, 0);
        $maxAttempts = config('eshaar.rate_limit.max_attempts', 3);

        return $attempts >= $maxAttempts;
    }

    /**
     * Update rate limit counter
     *
     * @return void
     */
    protected function updateRateLimit(string $phoneNumber)
    {
        $key = $this->getRateLimitCacheKey($phoneNumber);
        $attempts = Cache::get($key, 0) + 1;
        $window = config('eshaar.rate_limit.window_minutes', 60);

        Cache::put($key, $attempts, now()->addMinutes($window));
    }

    /**
     * Get rate limit cache key
     */
    protected function getRateLimitCacheKey(string $phoneNumber): string
    {
        return 'eshaar_rate_limit_' . $phoneNumber;
    }

    /**
     * Reset rate limit for phone number
     */
    public function resetRateLimit(string $phoneNumber): void
    {
        $key = $this->getRateLimitCacheKey($phoneNumber);
        Cache::forget($key);
    }

    /**
     * Check if OTP can be resent
     */
    public function canResend(string $phoneNumber): bool
    {
        return !$this->isRateLimited($phoneNumber);
    }
}