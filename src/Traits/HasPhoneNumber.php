<?php

namespace Monsefeledrisse\Eshaarsms\Traits;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Notifications\Notification;
use Monsefeledrisse\Eshaarsms\Facades\EshaarOtp;

trait HasPhoneNumber
{
    /**
     * Send OTP to this model's phone number
     *
     * @return array
     */
    public function sendOtp(array $options = []): array
    {
        $phoneNumber = $this->getPhoneNumberForOtp();
        
        if (!$phoneNumber) {
            throw new Exception('No phone number available for OTP');
        }

        return EshaarOtp::send($phoneNumber, $options);
    }

    /**
     * Verify OTP for this model's phone number
     *
     * @param string $code
     * @return array
     * @throws Exception
     */
    public function verifyOtp(string $code): array
    {
        $phoneNumber = $this->getPhoneNumberForOtp();
        
        if (!$phoneNumber) {
            throw new Exception('No phone number available for OTP verification');
        }

        return EshaarOtp::verify($phoneNumber, $code);
    }

    /**
     * Check if OTP can be resent for this model
     *
     * @return bool
     */
    public function canResendOtp(): bool
    {
        $phoneNumber = $this->getPhoneNumberForOtp();
        
        if (!$phoneNumber) {
            return false;
        }

        return EshaarOtp::canResend($phoneNumber);
    }

    /**
     * Reset rate limit for this model's phone number
     */
    public function resetOtpRateLimit(): void
    {
        $phoneNumber = $this->getPhoneNumberForOtp();
        
        if ($phoneNumber) {
            EshaarOtp::resetRateLimit($phoneNumber);
        }
    }

    /**
     * Get the phone number for OTP operations
     * Override this method in your model if needed
     *
     * @return string|null
     */
    protected function getPhoneNumberForOtp(): ?string
    {
        // Try common phone number field names
        $phoneFields = ['phone', 'phone_number', 'mobile', 'mobile_number'];
        
        foreach ($phoneFields as $field) {
            if (isset($this->attributes[$field]) && !empty($this->attributes[$field])) {
                return $this->attributes[$field];
            }
        }

        return null;
    }

    /**
     * Get OTP request statistics for this model
     *
     * @param int $days
     * @return array
     */
    public function getOtpStatistics(int $days = 7): array
    {
        $phoneNumber = $this->getPhoneNumberForOtp();

        if (!$phoneNumber) {
            return [];
        }

        return EshaarOtp::getStatistics($phoneNumber, $days);
    }

    /**
     * Get recent OTP requests for this model
     *
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public function getRecentOtpRequests(int $limit = 10): \Illuminate\Support\Collection
    {
        $phoneNumber = $this->getPhoneNumberForOtp();

        if (!$phoneNumber) {
            return collect();
        }

        return EshaarOtp::getRecentRequests($phoneNumber, $limit);
    }

    /**
     * Check if this model has any active OTP requests
     *
     * @return bool
     */
    public function hasActiveOtpRequest(): bool
    {
        $phoneNumber = $this->getPhoneNumberForOtp();

        if (!$phoneNumber) {
            return false;
        }

        return EshaarOtp::hasActiveRequest($phoneNumber);
    }

    /**
     * Route notifications for the Eshaar channel
     *
     * @param Notification $notification
     * @return string|null
     */
    public function routeNotificationForEshaar(Notification $notification): ?string
    {
        return $this->getPhoneNumberForOtp();
    }
}
