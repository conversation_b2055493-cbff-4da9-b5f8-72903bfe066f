<?php

namespace Mon<PERSON>eledrisse\Eshaarsms;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Monsefeledrisse\Eshaarsms\Exceptions\EshaarApiException;
use Monsefeledrisse\Eshaarsms\Exceptions\EshaarAuthenticationException;
use Monsefeledrisse\Eshaarsms\Exceptions\EshaarValidationException;

class EshaarClient
{
    protected $apiKey;
    protected $baseUrl;
    protected $sender;
    protected $paymentType;

    public function __construct()
    {
        $this->apiKey = config('eshaar.api_key');
        $this->baseUrl = config('eshaar.base_url', 'https://sms.lamah.com');
        $this->sender = config('eshaar.sender');
        $this->paymentType = config('eshaar.payment_type', 'postpaid');
    }

    /**
     * Initiate OTP sending via Eshaar API
     *
     * @param string $phoneNumber
     * @param string $language
     * @param int $length
     * @param int $expiration
     * @param string|null $sender
     * @return array
     * @throws EshaarApiException
     */
    public function initiateOtp($phoneNumber, $language = 'en', $length = 6, $expiration = 10, $sender = null)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/api/otp/initiate', [
            'receiver' => $phoneNumber,
            'lang' => $language,
            'length' => $length,
            'expiration' => $expiration,
            'sender' => $sender ?: $this->sender,
            'payment_type' => $this->paymentType,
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Verify OTP code via Eshaar API
     *
     * @param string $requestId
     * @param string $code
     * @return array
     * @throws EshaarApiException|ConnectionException
     */
    public function verifyOtp(string $requestId, string $code): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/api/otp/verify', [
            'request_id' => $requestId,
            'code' => $code,
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Send SMS message via Eshaar API
     *
     * @param string $phoneNumber
     * @param string $message
     * @param string|null $sender
     * @return array
     * @throws EshaarApiException|ConnectionException
     */
    public function sendSms(string $phoneNumber, string $message, string $sender = null): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/api/sms/messages', [
            'receiver' => $phoneNumber,
            'message' => $message,
            'sender' => $sender ?: $this->sender,
            'payment_type' => $this->paymentType,
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Handle API response and throw appropriate exceptions
     *
     * @param Response $response
     * @return array
     * @throws EshaarApiException
     */
    protected function handleResponse(Response $response): array
    {
        $data = $response->json();

        if ($response->successful()) {
            return $data;
        }

        // Handle different error types
        switch ($response->status()) {
            case 401:
                throw new EshaarAuthenticationException('Invalid API key or authentication failed');
            case 422:
                $errors = $data['errors'] ?? $data['message'] ?? 'Validation failed';
                throw new EshaarValidationException('Validation error: ' . json_encode($errors));
            default:
                $message = $data['message'] ?? 'API request failed';
                throw new EshaarApiException($message, $response->status());
        }
    }

    /**
     * Get account balance
     *
     * @return array
     * @throws EshaarApiException
     */
    public function getBalance(): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'application/json',
        ])->get($this->baseUrl . '/api/project/balance');

        return $this->handleResponse($response);
    }
}