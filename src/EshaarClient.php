<?php

namespace Monsefeledrisse\Eshaarsms;

use Illuminate\Support\Facades\Http;

class EshaarClient
{
    protected $apiKey;
    protected $baseUrl;
    protected $sender;

    public function __construct()
    {
        $this->apiKey = config('eshaar.api_key');
        $this->baseUrl = config('eshaar.base_url', 'https://sms.lamah.com/api');
        $this->sender = config('eshaar.sender');
    }

    public function sendSms($phoneNumber, $message)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'application/json',
        ])->post($this->baseUrl . '/send', [
            'recipient' => $phoneNumber,
            'sender' => $this->sender,
            'message' => $message,
        ]);

        return $response->json();
    }
}