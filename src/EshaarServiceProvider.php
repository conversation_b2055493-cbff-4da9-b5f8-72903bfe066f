<?php

namespace Monsefeledrisse\Eshaarsms;

use Illuminate\Support\ServiceProvider;
use Monsefeledrisse\Eshaarsms\Channels\EshaarSmsChannel;

class EshaarServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerConfig();

        // Register the Eshaar client
        $this->app->singleton(EshaarClient::class, fn($app): \Monsefeledrisse\Eshaarsms\EshaarClient => new EshaarClient());

        // Register the Eshaar client facade
        $this->app->singleton('eshaar.client', fn($app) => $app->make(EshaarClient::class));

        // Register the OTP service
        $this->app->singleton('eshaar.otp', fn($app): \Monsefeledrisse\Eshaarsms\OtpService => new OtpService($app->make(EshaarClient::class)));

        // Register the notification channel
        $this->app->bind(EshaarSmsChannel::class, fn($app): \Monsefeledrisse\Eshaarsms\Channels\EshaarSmsChannel => new EshaarSmsChannel($app->make(EshaarClient::class)));
    }

    public function boot(): void
    {
        // Register the notification channel
        $this->app->resolving(\Illuminate\Notifications\ChannelManager::class, function ($manager): void {
            $manager->extend('eshaar', fn($app) => $app->make(EshaarSmsChannel::class));
        });

        // Publish migrations
        $this->publishes([
            __DIR__.'/../database/migrations/' => database_path('migrations')
        ], ['eshaar', 'eshaar:migrations']);
    }

    protected function registerConfig()
    {
        $config = __DIR__.'/../config/eshaar.php';

        $this->publishes([$config => base_path('config/eshaar.php')], ['eshaar', 'eshaar:config']);

        $this->mergeConfigFrom($config, 'eshaar');
    }
}