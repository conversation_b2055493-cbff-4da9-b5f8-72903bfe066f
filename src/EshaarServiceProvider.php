<?php

namespace Monsefeledrisse\Eshaarsms;

use Illuminate\Support\ServiceProvider;
use Monsefeledrisse\Eshaarsms\Channels\EshaarSmsChannel;

class EshaarServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->registerConfig();
        
        // Register the Eshaar client
        $this->app->singleton(EshaarClient::class, function ($app) {
            return new EshaarClient();
        });
        
        // Register the OTP service
        $this->app->singleton('eshaar.otp', function ($app) {
            return new OtpService($app->make(EshaarClient::class));
        });
        
        // Register the notification channel
        $this->app->bind(EshaarSmsChannel::class, function ($app) {
            return new EshaarSmsChannel($app->make(EshaarClient::class));
        });
    }

    public function boot()
    {
        // Register the notification channel
        $this->app->resolving('Illuminate\Notifications\ChannelManager', function ($manager) {
            $manager->extend('eshaar', function ($app) {
                return $app->make(EshaarSmsChannel::class);
            });
        });
    }

    protected function registerConfig()
    {
        $config = __DIR__.'/../config/eshaar.php';

        $this->publishes([$config => base_path('config/eshaar.php')], ['eshaar', 'eshaar:config']);

        $this->mergeConfigFrom($config, 'eshaar');
    }
}