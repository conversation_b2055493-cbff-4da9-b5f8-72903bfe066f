<?php

namespace Monsefeledrisse\Eshaarsms\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class OtpRequest extends Model
{
    protected $fillable = [
        'phone_number',
        'request_id',
        'expires_at',
        'status',
        'metadata',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'metadata' => 'array',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_VERIFIED = 'verified';
    const STATUS_EXPIRED = 'expired';
    const STATUS_FAILED = 'failed';

    /**
     * Scope to get active (non-expired) OTP requests
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('expires_at', '>', now())
                    ->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get expired OTP requests
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now())
                    ->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to filter by phone number
     */
    public function scopeForPhoneNumber(Builder $query, string $phoneNumber): Builder
    {
        return $query->where('phone_number', $phoneNumber);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Check if the OTP request is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the OTP request is active (not expired and pending)
     */
    public function isActive(): bool
    {
        return !$this->isExpired() && $this->status === self::STATUS_PENDING;
    }

    /**
     * Mark the OTP request as verified
     */
    public function markAsVerified(): bool
    {
        return $this->update(['status' => self::STATUS_VERIFIED]);
    }

    /**
     * Mark the OTP request as failed
     */
    public function markAsFailed(): bool
    {
        return $this->update(['status' => self::STATUS_FAILED]);
    }

    /**
     * Mark the OTP request as expired
     */
    public function markAsExpired(): bool
    {
        return $this->update(['status' => self::STATUS_EXPIRED]);
    }

    /**
     * Get the latest active OTP request for a phone number
     */
    public static function getActiveForPhoneNumber(string $phoneNumber): ?self
    {
        return static::forPhoneNumber($phoneNumber)
                    ->active()
                    ->latest()
                    ->first();
    }

    /**
     * Create a new OTP request
     */
    public static function createRequest(
        string $phoneNumber,
        string $requestId,
        int $expiryMinutes,
        array $metadata = []
    ): self {
        return static::create([
            'phone_number' => $phoneNumber,
            'request_id' => $requestId,
            'expires_at' => now()->addMinutes($expiryMinutes),
            'status' => self::STATUS_PENDING,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Clean up expired OTP requests
     */
    public static function cleanupExpired(): int
    {
        // Mark expired requests as expired
        $expiredCount = static::expired()->update(['status' => self::STATUS_EXPIRED]);

        // Optionally delete old records (older than 30 days)
        $deletedCount = static::where('created_at', '<', now()->subDays(30))->delete();

        return $expiredCount;
    }

    /**
     * Get statistics for OTP requests
     */
    public static function getStatistics(string $phoneNumber = null, int $days = 7): array
    {
        $query = static::where('created_at', '>=', now()->subDays($days));

        if ($phoneNumber) {
            $query->forPhoneNumber($phoneNumber);
        }

        $total = $query->count();
        $verified = $query->clone()->withStatus(self::STATUS_VERIFIED)->count();
        $expired = $query->clone()->withStatus(self::STATUS_EXPIRED)->count();
        $failed = $query->clone()->withStatus(self::STATUS_FAILED)->count();
        $pending = $query->clone()->withStatus(self::STATUS_PENDING)->count();

        return [
            'total' => $total,
            'verified' => $verified,
            'expired' => $expired,
            'failed' => $failed,
            'pending' => $pending,
            'success_rate' => $total > 0 ? round(($verified / $total) * 100, 2) : 0,
        ];
    }
}
