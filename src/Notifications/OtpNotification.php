<?php

namespace Monsefeledrisse\Eshaarsms\Notifications;

use Illuminate\Notifications\Notification;
use Monsefeledrisse\Eshaarsms\Channels\EshaarSmsChannel;

class OtpNotification extends Notification
{
    /**
     * Create a new notification instance.
     *
     * @param string $otp
     * @return void
     */
    public function __construct(protected string $otp)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via(mixed $notifiable): array
    {
        return ['eshaar'];
    }

    /**
     * Get the Eshaar SMS representation of the notification.
     *
     * @param mixed $notifiable
     * @return string
     */
    public function toEshaar(mixed $notifiable)
    {
        $template = config('eshaar.message_template', 'Your verification code is: {otp}');
        return str_replace('{otp}', $this->otp, $template);
    }
}