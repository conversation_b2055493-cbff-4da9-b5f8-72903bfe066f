<?php

namespace Monsefeledrisse\Eshaarsms\Notifications;

use Illuminate\Notifications\Notification;
use Monsefeledrisse\Eshaarsms\Channels\EshaarSmsChannel;

class OtpNotification extends Notification
{
    protected $otp;

    /**
     * Create a new notification instance.
     *
     * @param string $otp
     * @return void
     */
    public function __construct($otp)
    {
        $this->otp = $otp;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['eshaar'];
    }

    /**
     * Get the Eshaar SMS representation of the notification.
     *
     * @param mixed $notifiable
     * @return string
     */
    public function toEshaar($notifiable)
    {
        $template = config('eshaar.message_template', 'Your verification code is: {otp}');
        return str_replace('{otp}', $this->otp, $template);
    }
}