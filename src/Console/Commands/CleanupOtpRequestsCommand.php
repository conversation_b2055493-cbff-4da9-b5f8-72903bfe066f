<?php

namespace Monsefeledrisse\Eshaarsms\Console\Commands;

use Illuminate\Console\Command;
use Mon<PERSON>eledrisse\Eshaarsms\Models\OtpRequest;

class CleanupOtpRequestsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'eshaar:cleanup-otp-requests 
                            {--days=30 : Delete OTP requests older than this many days}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old OTP requests from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        $this->info("Cleaning up OTP requests older than {$days} days...");

        // Mark expired requests as expired
        $expiredCount = OtpRequest::expired()->update(['status' => OtpRequest::STATUS_EXPIRED]);
        
        if ($expiredCount > 0) {
            $this->info("Marked {$expiredCount} expired OTP requests as expired.");
        }

        // Get old records to delete
        $oldRecords = OtpRequest::where('created_at', '<', now()->subDays($days));
        $oldCount = $oldRecords->count();

        if ($oldCount === 0) {
            $this->info('No old OTP requests found to clean up.');
            return 0;
        }

        if ($dryRun) {
            $this->warn("DRY RUN: Would delete {$oldCount} OTP requests older than {$days} days.");
            
            // Show some examples
            $examples = $oldRecords->limit(5)->get(['id', 'phone_number', 'status', 'created_at']);
            
            if ($examples->isNotEmpty()) {
                $this->info('Examples of records that would be deleted:');
                $this->table(
                    ['ID', 'Phone Number', 'Status', 'Created At'],
                    $examples->map(fn($record) => [
                        $record->id,
                        $record->phone_number,
                        $record->status,
                        $record->created_at->format('Y-m-d H:i:s')
                    ])->toArray()
                );
            }
            
            return 0;
        }

        if (!$this->confirm("Are you sure you want to delete {$oldCount} OTP requests?")) {
            $this->info('Operation cancelled.');
            return 0;
        }

        // Delete old records
        $deletedCount = $oldRecords->delete();

        $this->info("Successfully deleted {$deletedCount} old OTP requests.");

        // Show statistics
        $this->showStatistics();

        return 0;
    }

    /**
     * Show current OTP request statistics
     */
    protected function showStatistics()
    {
        $stats = OtpRequest::getStatistics();

        $this->info('Current OTP Request Statistics (last 7 days):');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Requests', $stats['total']],
                ['Verified', $stats['verified']],
                ['Expired', $stats['expired']],
                ['Failed', $stats['failed']],
                ['Pending', $stats['pending']],
                ['Success Rate', $stats['success_rate'] . '%'],
            ]
        );
    }
}
