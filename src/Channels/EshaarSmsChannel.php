<?php

namespace Monsefeledrisse\Eshaarsms\Channels;

use Illuminate\Notifications\Notification;
use Monsefeledrisse\Eshaarsms\EshaarClient;

class EshaarSmsChannel
{
    protected $client;

    public function __construct(EshaarClient $client)
    {
        $this->client = $client;
    }

    /**
     * Send the given notification.
     *
     * @param mixed $notifiable
     * @param \Illuminate\Notifications\Notification $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        if (!method_exists($notification, 'toEshaar')) {
            throw new \Exception('Notification does not have toEshaar method');
        }

        $message = $notification->toEshaar($notifiable);
        $phoneNumber = $notifiable->routeNotificationForEshaar($notification);

        if (empty($phoneNumber)) {
            throw new \Exception('No phone number provided for Eshaar SMS notification');
        }

        $this->client->sendSms($phoneNumber, $message);
    }
}