<?php

/**
 * Database Usage Examples for Eshaar SMS Package
 * 
 * This file demonstrates the new database functionality
 * for OTP request tracking and management.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Monsefeledrisse\Eshaarsms\Facades\EshaarOtp;
use Monsefeledrisse\Eshaarsms\Models\OtpRequest;

echo "=== Database Usage Examples ===\n\n";

// Example 1: Send OTP and track in database
echo "=== Example 1: Send OTP with Database Tracking ===\n";

try {
    $phoneNumber = '218912345678';
    $result = EshaarOtp::send($phoneNumber, [
        'language' => 'en',
        'length' => 6,
        'expiry' => 10
    ]);
    
    if ($result['success']) {
        echo "✅ OTP sent successfully!\n";
        echo "📱 Phone: {$phoneNumber}\n";
        echo "🆔 Request ID: {$result['request_id']}\n";
        echo "💰 Cost: {$result['cost']}\n";
        echo "🗄️ Database Record ID: {$result['otp_request_id']}\n";
        
        // Check if phone number has active request
        if (EshaarOtp::hasActiveRequest($phoneNumber)) {
            echo "✅ Active OTP request confirmed in database\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 2: Get OTP Statistics
echo "=== Example 2: OTP Statistics ===\n";

try {
    $stats = EshaarOtp::getStatistics($phoneNumber, 7); // Last 7 days
    
    echo "📊 OTP Statistics for {$phoneNumber} (last 7 days):\n";
    echo "   Total Requests: {$stats['total']}\n";
    echo "   Verified: {$stats['verified']}\n";
    echo "   Expired: {$stats['expired']}\n";
    echo "   Failed: {$stats['failed']}\n";
    echo "   Pending: {$stats['pending']}\n";
    echo "   Success Rate: {$stats['success_rate']}%\n";
    
    // Get overall statistics
    $overallStats = EshaarOtp::getAllStatistics(7);
    echo "\n📈 Overall Statistics (last 7 days):\n";
    echo "   Total Requests: {$overallStats['total']}\n";
    echo "   Success Rate: {$overallStats['success_rate']}%\n";
    
} catch (Exception $e) {
    echo "❌ Error getting statistics: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 3: Get Recent OTP Requests
echo "=== Example 3: Recent OTP Requests ===\n";

try {
    $recentRequests = EshaarOtp::getRecentRequests($phoneNumber, 5);
    
    echo "📋 Recent OTP requests for {$phoneNumber}:\n";
    
    if ($recentRequests->isEmpty()) {
        echo "   No recent requests found.\n";
    } else {
        foreach ($recentRequests as $request) {
            $metadata = $request->metadata ?? [];
            echo "   🔹 ID: {$request->id}\n";
            echo "      Request ID: {$request->request_id}\n";
            echo "      Status: {$request->status}\n";
            echo "      Created: {$request->created_at->format('Y-m-d H:i:s')}\n";
            echo "      Expires: {$request->expires_at->format('Y-m-d H:i:s')}\n";
            echo "      Language: " . ($metadata['language'] ?? 'N/A') . "\n";
            echo "      Length: " . ($metadata['length'] ?? 'N/A') . "\n";
            echo "      Cost: " . ($metadata['cost'] ?? 'N/A') . "\n";
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error getting recent requests: " . $e->getMessage() . "\n";
}

// Example 4: Direct Database Queries
echo "=== Example 4: Direct Database Queries ===\n";

try {
    // Get all pending requests
    $pendingRequests = OtpRequest::withStatus(OtpRequest::STATUS_PENDING)->count();
    echo "📊 Pending OTP requests: {$pendingRequests}\n";
    
    // Get expired requests
    $expiredRequests = OtpRequest::expired()->count();
    echo "⏰ Expired OTP requests: {$expiredRequests}\n";
    
    // Get requests for specific phone number
    $phoneRequests = OtpRequest::forPhoneNumber($phoneNumber)->count();
    echo "📱 Total requests for {$phoneNumber}: {$phoneRequests}\n";
    
    // Get active request for phone number
    $activeRequest = OtpRequest::getActiveForPhoneNumber($phoneNumber);
    if ($activeRequest) {
        echo "✅ Active request found: {$activeRequest->request_id}\n";
        echo "   Expires at: {$activeRequest->expires_at->format('Y-m-d H:i:s')}\n";
        echo "   Time remaining: " . $activeRequest->expires_at->diffForHumans() . "\n";
    } else {
        echo "❌ No active request found for {$phoneNumber}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error with database queries: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 5: Cleanup Operations
echo "=== Example 5: Cleanup Operations ===\n";

try {
    // Clean up expired requests
    $cleanedCount = EshaarOtp::cleanup();
    echo "🧹 Cleaned up {$cleanedCount} expired OTP requests\n";
    
    // Show statistics after cleanup
    $stats = EshaarOtp::getAllStatistics(30); // Last 30 days
    echo "📊 Statistics after cleanup (last 30 days):\n";
    echo "   Total: {$stats['total']}\n";
    echo "   Pending: {$stats['pending']}\n";
    echo "   Expired: {$stats['expired']}\n";
    echo "   Verified: {$stats['verified']}\n";
    
} catch (Exception $e) {
    echo "❌ Error during cleanup: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 6: Model Integration
echo "=== Example 6: Model Integration Example ===\n";

// This would be used in your actual User model
class ExampleUser
{
    use \Monsefeledrisse\Eshaarsms\Traits\HasPhoneNumber;
    
    protected $attributes = [
        'phone' => '218912345678'
    ];
    
    protected function getPhoneNumberForOtp()
    {
        return $this->attributes['phone'];
    }
}

try {
    $user = new ExampleUser();
    
    // Check if user has active OTP request
    if ($user->hasActiveOtpRequest()) {
        echo "👤 User has an active OTP request\n";
        
        // Get user's OTP statistics
        $userStats = $user->getOtpStatistics(7);
        echo "📊 User's OTP stats: {$userStats['total']} total, {$userStats['success_rate']}% success rate\n";
        
        // Get user's recent requests
        $userRequests = $user->getRecentOtpRequests(3);
        echo "📋 User's recent requests: {$userRequests->count()} found\n";
    } else {
        echo "👤 User has no active OTP requests\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error with model integration: " . $e->getMessage() . "\n";
}

echo "\n=== Database Examples completed ===\n";
